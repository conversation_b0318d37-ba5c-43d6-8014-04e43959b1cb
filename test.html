<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账号切换插件测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1 {
            color: #333;
            text-align: center;
        }

        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .test-section h3 {
            margin-top: 0;
            color: #666;
        }

        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }

        button:hover {
            background: #0056b3;
        }

        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>账号切换插件测试页面</h1>

        <div class="test-section">
            <h3>1. 浏览器兼容性检查</h3>
            <div id="compatibility-status"></div>
        </div>

        <div class="test-section">
            <h3>2. 插件加载状态</h3>
            <div id="plugin-status"></div>
            <button onclick="checkPluginStatus()">检查插件状态</button>
        </div>

        <div class="test-section">
            <h3>3. 浮动按钮测试</h3>
            <div id="button-status"></div>
            <p>请查看页面左侧是否有小的圆形按钮，尝试点击它。</p>
            <button onclick="testButtonClick()">模拟点击测试</button>
        </div>

        <div class="test-section">
            <h3>4. 模态框测试</h3>
            <div id="modal-status"></div>
            <button onclick="checkModalStatus()">检查模态框状态</button>
            <button onclick="checkModalStyles()">检查模态框样式</button>
        </div>

        <div class="test-section">
            <h3>5. 控制台日志</h3>
            <div id="console-log" class="log"></div>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="test-section">
            <h3>6. 手动测试步骤</h3>
            <ol>
                <li>检查浏览器兼容性（上方显示）</li>
                <li>查看插件是否正确加载</li>
                <li>在页面左侧查找小的圆形按钮</li>
                <li>鼠标悬停按钮，应该完全显示</li>
                <li>点击按钮，应该打开主弹窗</li>
                <li>查看控制台日志确认事件触发</li>
            </ol>
        </div>
    </div>

    <script>
        // 拦截console.log来显示日志
        const originalLog = console.log;
        const logContainer = document.getElementById('console-log');

        console.log = function(...args) {
            originalLog.apply(console, args);
            const message = args.map(arg =>
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            logContainer.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            logContainer.scrollTop = logContainer.scrollHeight;
        };

        function clearLog() {
            logContainer.textContent = '';
        }

        function checkCompatibility() {
            const compatibilityDiv = document.getElementById('compatibility-status');
            let html = '';

            // 检查Shadow DOM支持
            if (typeof Element.prototype.attachShadow === 'function') {
                html += '<div class="status success">✅ 浏览器支持Shadow DOM</div>';
            } else {
                html += '<div class="status error">❌ 浏览器不支持Shadow DOM</div>';
            }

            // 检查Custom Elements支持
            if (typeof customElements !== 'undefined') {
                html += '<div class="status success">✅ 浏览器支持Custom Elements</div>';
            } else {
                html += '<div class="status error">❌ 浏览器不支持Custom Elements</div>';
            }

            // 检查浏览器版本
            const userAgent = navigator.userAgent;
            html += '<div class="status info">浏览器信息: ' + userAgent + '</div>';

            compatibilityDiv.innerHTML = html;
        }

        function checkPluginStatus() {
            const statusDiv = document.getElementById('plugin-status');
            const element = document.querySelector('account-change');

            if (element) {
                statusDiv.innerHTML = '<div class="status success">✅ 插件元素已找到</div>';
                console.log('插件元素:', element);
            } else {
                statusDiv.innerHTML = '<div class="status error">❌ 未找到插件元素</div>';
            }
        }

        function testButtonClick() {
            console.log('=== 开始模拟点击测试 ===');
            const element = document.querySelector('account-change');
            if (!element) {
                console.error('未找到插件元素');
                return;
            }

            // 由于是closed shadow root，无法直接访问内部元素
            // 但可以通过日志观察点击效果
            console.log('请手动点击浮动按钮，观察日志输出');
        }

        function checkModalStatus() {
            const statusDiv = document.getElementById('modal-status');

            // 检查是否有模态框相关的日志
            const logContent = document.getElementById('console-log').textContent;

            if (logContent.includes('Modal is now visible')) {
                statusDiv.innerHTML = '<div class="status success">✅ 模态框已显示</div>';
            } else if (logContent.includes('showModal called')) {
                statusDiv.innerHTML = '<div class="status info">ℹ️ 模态框创建中，请检查日志</div>';
            } else {
                statusDiv.innerHTML = '<div class="status error">❌ 未检测到模态框活动</div>';
            }
        }

        function checkModalStyles() {
            const statusDiv = document.getElementById('modal-status');
            const logContent = document.getElementById('console-log').textContent;

            if (logContent.includes('Modal is now visible')) {
                statusDiv.innerHTML = `
                    <div class="status success">✅ 模态框已显示</div>
                    <div class="status info">
                        <strong>样式检查提示：</strong><br>
                        1. 模态框应该显示为800×650px<br>
                        2. 应该有完整的标签页和表单样式<br>
                        3. 按钮应该有悬停效果<br>
                        4. 滚动区域应该有自定义滚动条<br>
                        请在Elements面板中检查.modal元素的样式
                    </div>
                `;
            } else {
                statusDiv.innerHTML = '<div class="status error">❌ 请先打开模态框再检查样式</div>';
            }
        }

        // 页面加载完成后检查兼容性
        document.addEventListener('DOMContentLoaded', function() {
            checkCompatibility();

            // 延迟检查插件状态
            setTimeout(() => {
                checkPluginStatus();
            }, 1000);
        });

        // 监听插件相关的日志
        window.addEventListener('load', function() {
            console.log('测试页面加载完成');
        });
    </script>

    <!-- 加载插件 -->
    <script src="dist/account-change.user.js"></script>
</body>
</html>
