import { defineConfig } from 'vite';
import monkey from 'vite-plugin-monkey';

// https://vitejs.dev/config/
export default defineConfig({
  build: {
    minify: 'esbuild'
  },
  plugins: [
    monkey({
      entry: 'src/main.ts',
      userscript: {
        icon: 'https://vitejs.dev/logo.svg',
        namespace: 'npm/vite-plugin-monkey',
        match: ['http://localhost:9100/*',
          'https://cq24.91cloudpay.com:4443/*',
          'http://portal.local:8080/*',
          'https://kkl-dev.91cloudpay.com:4443/*',
          'https://kkl.91cloudpay.com:4443/*'
        ],
        grant: ['GM_addStyle']
      }
    }),
  ],
});
