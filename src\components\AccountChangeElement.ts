import { FloatingButton } from './FloatingButton';
import { Mo<PERSON>, TabContainer } from './Modal';
import { AccountPanel } from './AccountPanel';
import { RequestMonitor } from './RequestMonitor';
import { requestInterceptor } from '../utils/interceptor';

/**
 * 账号切换主组件 - Shadow DOM实现
 * 使用Shadow DOM实现完全的样式隔离
 */
export class AccountChangeElement extends HTMLElement {
  private shadow: ShadowRoot;
  private floatingButton: FloatingButton | null = null;
  private modal: Modal | null = null;
  private tabContainer: TabContainer | null = null;
  private accountPanel: AccountPanel | null = null;
  private requestMonitor: RequestMonitor | null = null;

  constructor() {
    super();

    // 创建Shadow DOM
    this.shadow = this.attachShadow({ mode: 'closed' });

    // 初始化
    this.init();
  }

  /**
   * 初始化组件
   */
  private init(): void {
    // 注入样式
    this.injectStyles();

    // 启动请求拦截器
    requestInterceptor.start();

    // 创建悬浮按钮
    this.createFloatingButton();

    console.log('账号切换插件已启动 (Shadow DOM)');
  }

  /**
   * 注入样式到Shadow DOM
   */
  private injectStyles(): void {
    const style = document.createElement('style');
    const styles = this.getStyles();
    style.textContent = styles;
    this.shadow.appendChild(style);

    // 调试：输出样式长度和前100个字符
    console.log('样式注入完成:', {
      stylesLength: styles.length,
      stylesPreview: styles.substring(0, 100),
      shadowChildren: this.shadow.children.length
    });
  }

  /**
   * 获取所有样式 - 现代化重建版本
   */
  private getStyles(): string {
    return `
      /* 重置样式 */
      :host {
        all: initial;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'SF Pro Display', Roboto, 'Helvetica Neue', Arial, sans-serif;
        display: block;
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: 999999;
        pointer-events: none;
      }

      *, *::before, *::after {
        box-sizing: border-box;
      }

      /* 现代化CSS变量系统 */
      :host {
        /* 主色调 - 现代紫色系 */
        --primary-50: #f5f3ff;
        --primary-100: #ede9fe;
        --primary-200: #ddd6fe;
        --primary-300: #c4b5fd;
        --primary-400: #a78bfa;
        --primary-500: #8b5cf6;
        --primary-600: #7c3aed;
        --primary-700: #6d28d9;
        --primary-800: #5b21b6;
        --primary-900: #4c1d95;

        /* 中性色 - 现代灰色系 */
        --gray-50: #f9fafb;
        --gray-100: #f3f4f6;
        --gray-200: #e5e7eb;
        --gray-300: #d1d5db;
        --gray-400: #9ca3af;
        --gray-500: #6b7280;
        --gray-600: #4b5563;
        --gray-700: #374151;
        --gray-800: #1f2937;
        --gray-900: #111827;

        /* 语义色彩 */
        --success-color: #10b981;
        --success-light: #d1fae5;
        --danger-color: #ef4444;
        --danger-light: #fee2e2;
        --warning-color: #f59e0b;
        --warning-light: #fef3c7;
        --info-color: #3b82f6;
        --info-light: #dbeafe;

        /* 背景色 */
        --bg-primary: #ffffff;
        --bg-secondary: var(--gray-50);
        --bg-tertiary: var(--gray-100);
        --bg-glass: rgba(255, 255, 255, 0.8);
        --bg-overlay: rgba(0, 0, 0, 0.4);

        /* 文字色 */
        --text-primary: var(--gray-900);
        --text-secondary: var(--gray-600);
        --text-tertiary: var(--gray-400);
        --text-inverse: #ffffff;

        /* 边框色 */
        --border-light: var(--gray-200);
        --border-medium: var(--gray-300);
        --border-strong: var(--gray-400);

        /* 圆角系统 */
        --radius-xs: 0.25rem;
        --radius-sm: 0.375rem;
        --radius-md: 0.5rem;
        --radius-lg: 0.75rem;
        --radius-xl: 1rem;
        --radius-2xl: 1.5rem;
        --radius-full: 9999px;

        /* 阴影系统 */
        --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
        --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

        /* 间距系统 */
        --space-1: 0.25rem;
        --space-2: 0.5rem;
        --space-3: 0.75rem;
        --space-4: 1rem;
        --space-5: 1.25rem;
        --space-6: 1.5rem;
        --space-8: 2rem;
        --space-10: 2.5rem;
        --space-12: 3rem;

        /* 动画缓动 */
        --ease-out: cubic-bezier(0.0, 0.0, 0.2, 1);
        --ease-in: cubic-bezier(0.4, 0.0, 1, 1);
        --ease-in-out: cubic-bezier(0.4, 0.0, 0.2, 1);
        --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
      }

      /* 现代化悬浮按钮 */
      .floating-button {
        position: fixed;
        top: 50%;
        left: -20px;
        transform: translateY(-50%);
        z-index: 10000;
        width: 48px;
        height: 48px;
        background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
        border: none;
        border-radius: var(--radius-full);
        cursor: pointer;
        transition: all 0.4s var(--ease-out);
        box-shadow: var(--shadow-lg);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text-inverse);
        pointer-events: auto;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .floating-button::before {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: inherit;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));
        opacity: 0;
        transition: opacity 0.3s var(--ease-out);
      }

      .floating-button:hover {
        left: -8px !important;
        transform: translateY(-50%) scale(1.05);
        box-shadow: var(--shadow-xl);
        background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
      }

      .floating-button:hover::before {
        opacity: 1;
      }

      .floating-button:active {
        transform: translateY(-50%) scale(0.98);
        transition: all 0.1s var(--ease-in);
      }

      .floating-button.dragging {
        transition: none;
        box-shadow: var(--shadow-2xl);
      }

      .floating-button .icon {
        width: 20px;
        height: 20px;
        fill: currentColor;
        transition: transform 0.3s var(--ease-out);
      }

      .floating-button:hover .icon {
        transform: rotate(180deg) scale(1.1);
      }

      /* 现代化毛玻璃模态框 */
      .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--bg-overlay);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        z-index: 10001;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        visibility: hidden;
        transition: all 0.4s var(--ease-out);
        pointer-events: auto;
        padding: var(--space-4);
      }

      .modal-overlay.show {
        opacity: 1;
        visibility: visible;
      }

      .modal {
        background: var(--bg-glass);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: var(--radius-2xl);
        box-shadow: var(--shadow-2xl);
        width: 100%;
        max-width: 900px;
        max-height: 85vh;
        overflow: hidden;
        transform: scale(0.9) translateY(20px);
        transition: all 0.4s var(--ease-bounce);
        position: relative;
      }

      .modal::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
      }

      .modal-overlay.show .modal {
        transform: scale(1) translateY(0);
      }

      /* 现代化模态框头部 */
      .modal-header {
        padding: var(--space-6) var(--space-6) var(--space-4);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        display: flex;
        align-items: center;
        justify-content: space-between;
        min-height: 80px;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
      }

      .modal-header h2 {
        margin: 0;
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--text-primary);
        background: linear-gradient(135deg, var(--primary-600), var(--primary-800));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      /* 现代化标签页系统 */
      .header-tabs {
        display: flex;
        gap: var(--space-1);
        flex: 1;
        background: rgba(255, 255, 255, 0.1);
        border-radius: var(--radius-xl);
        padding: var(--space-1);
        backdrop-filter: blur(10px);
      }

      .header-tab {
        padding: var(--space-3) var(--space-5);
        background: none;
        border: none;
        cursor: pointer;
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--text-secondary);
        border-radius: var(--radius-lg);
        transition: all 0.3s var(--ease-out);
        position: relative;
        flex: 1;
        text-align: center;
      }

      .header-tab::before {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: inherit;
        background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
        opacity: 0;
        transition: opacity 0.3s var(--ease-out);
      }

      .header-tab.active {
        color: var(--text-inverse);
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
      }

      .header-tab.active::before {
        opacity: 1;
      }

      .header-tab:hover:not(.active) {
        color: var(--text-primary);
        background: rgba(255, 255, 255, 0.1);
        transform: translateY(-1px);
      }

      .close-btn {
        background: rgba(255, 255, 255, 0.1);
        border: none;
        width: 32px;
        height: 32px;
        border-radius: var(--radius-full);
        cursor: pointer;
        color: var(--text-secondary);
        transition: all 0.3s var(--ease-out);
        margin-left: var(--space-4);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
      }

      .close-btn:hover {
        background: rgba(255, 255, 255, 0.2);
        color: var(--text-primary);
        transform: scale(1.1);
      }

      /* 现代化模态框主体 */
      .modal-body {
        padding: var(--space-6);
        max-height: 65vh;
        overflow-y: auto;
        background: rgba(255, 255, 255, 0.02);
      }

      .modal-body::-webkit-scrollbar {
        width: 6px;
      }

      .modal-body::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: var(--radius-full);
      }

      .modal-body::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3);
        border-radius: var(--radius-full);
      }

      .modal-body::-webkit-scrollbar-thumb:hover {
        background: rgba(255, 255, 255, 0.5);
      }
      /* 现代化标签页内容 */
      .tab-container.header-mode .tabs {
        display: none;
      }

      .tabs {
        display: flex;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        margin-bottom: var(--space-6);
        background: rgba(255, 255, 255, 0.05);
        border-radius: var(--radius-lg) var(--radius-lg) 0 0;
        padding: 0 var(--space-2);
      }

      .tab {
        padding: var(--space-4) var(--space-6);
        background: none;
        border: none;
        cursor: pointer;
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--text-secondary);
        border-bottom: 3px solid transparent;
        transition: all 0.3s var(--ease-out);
        position: relative;
      }

      .tab::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
        border-radius: var(--radius-full);
        transition: all 0.3s var(--ease-out);
        transform: translateX(-50%);
      }

      .tab.active {
        color: var(--primary-600);
      }

      .tab.active::before {
        width: 100%;
      }

      .tab:hover:not(.active) {
        color: var(--text-primary);
        background: rgba(255, 255, 255, 0.1);
      }

      .tab-content {
        display: none;
        animation: fadeIn 0.3s var(--ease-out);
      }

      .tab-content.active {
        display: block;
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* 现代化表单系统 */
      .form-group {
        margin-bottom: var(--space-6);
        position: relative;
      }

      .form-label {
        display: block;
        margin-bottom: var(--space-2);
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--text-primary);
        background: linear-gradient(135deg, var(--text-primary), var(--text-secondary));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .form-input {
        width: 100%;
        padding: var(--space-4) var(--space-5);
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-radius: var(--radius-xl);
        font-size: 0.875rem;
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(10px);
        color: var(--text-primary);
        transition: all 0.3s var(--ease-out);
        position: relative;
      }

      .form-input::before {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: inherit;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
        opacity: 0;
        transition: opacity 0.3s var(--ease-out);
        pointer-events: none;
      }

      .form-input:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 4px rgba(139, 92, 246, 0.1);
        background: rgba(255, 255, 255, 0.1);
        transform: translateY(-1px);
      }

      .form-input:focus::before {
        opacity: 1;
      }

      .form-input::placeholder {
        color: var(--text-tertiary);
        transition: color 0.3s var(--ease-out);
      }

      .form-input:focus::placeholder {
        color: var(--text-secondary);
      }

      /* 现代化按钮系统 */
      .btn {
        padding: var(--space-4) var(--space-6);
        border: none;
        border-radius: var(--radius-xl);
        font-size: 0.875rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s var(--ease-out);
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: var(--space-2);
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(10px);
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .btn::before {
        content: '';
        position: absolute;
        inset: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));
        opacity: 0;
        transition: opacity 0.3s var(--ease-out);
      }

      .btn:hover::before {
        opacity: 1;
      }

      .btn:active {
        transform: scale(0.98);
        transition: transform 0.1s var(--ease-in);
      }

      .btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
      }

      .btn:disabled::before {
        opacity: 0;
      }

      /* 主要按钮 */
      .btn.btn-primary {
        background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
        color: var(--text-inverse);
        box-shadow: var(--shadow-md);
      }

      .btn.btn-primary:hover:not(:disabled) {
        background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
        box-shadow: var(--shadow-lg);
        transform: translateY(-2px);
      }

      /* 次要按钮 */
      .btn.btn-secondary {
        background: rgba(255, 255, 255, 0.1);
        color: var(--text-primary);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .btn.btn-secondary:hover:not(:disabled) {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.3);
        transform: translateY(-1px);
      }

      /* 危险按钮 */
      .btn.btn-danger {
        background: linear-gradient(135deg, var(--danger-color), #dc2626);
        color: var(--text-inverse);
        box-shadow: var(--shadow-md);
      }

      .btn.btn-danger:hover:not(:disabled) {
        background: linear-gradient(135deg, #dc2626, #b91c1c);
        box-shadow: var(--shadow-lg);
        transform: translateY(-2px);
      }

      /* 小尺寸按钮 */
      .btn.btn-sm {
        padding: var(--space-2) var(--space-4);
        font-size: 0.75rem;
        border-radius: var(--radius-lg);
      }

      /* 按钮组 */
      .btn-group {
        display: flex;
        gap: var(--space-3);
        margin-top: var(--space-6);
        flex-wrap: wrap;
      }

      /* 现代化加载状态 */
      .loading {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 3px solid rgba(255, 255, 255, 0.1);
        border-top: 3px solid var(--primary-500);
        border-radius: var(--radius-full);
        animation: modernSpin 1s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
      }

      @keyframes modernSpin {
        0% {
          transform: rotate(0deg) scale(1);
        }
        50% {
          transform: rotate(180deg) scale(1.1);
        }
        100% {
          transform: rotate(360deg) scale(1);
        }
      }

      /* 现代化工具类 */
      .text-center { text-align: center; }
      .text-right { text-align: right; }
      .text-left { text-align: left; }

      .mt-1 { margin-top: var(--space-1); }
      .mt-2 { margin-top: var(--space-2); }
      .mt-3 { margin-top: var(--space-3); }
      .mt-4 { margin-top: var(--space-4); }
      .mt-6 { margin-top: var(--space-6); }

      .mb-1 { margin-bottom: var(--space-1); }
      .mb-2 { margin-bottom: var(--space-2); }
      .mb-3 { margin-bottom: var(--space-3); }
      .mb-4 { margin-bottom: var(--space-4); }
      .mb-6 { margin-bottom: var(--space-6); }

      .text-xs { font-size: 0.75rem; }
      .text-sm { font-size: 0.875rem; }
      .text-base { font-size: 1rem; }
      .text-lg { font-size: 1.125rem; }

      .font-normal { font-weight: 400; }
      .font-medium { font-weight: 500; }
      .font-semibold { font-weight: 600; }
      .font-bold { font-weight: 700; }

      .text-primary { color: var(--text-primary); }
      .text-secondary { color: var(--text-secondary); }
      .text-tertiary { color: var(--text-tertiary); }
      .text-success { color: var(--success-color); }
      .text-danger { color: var(--danger-color); }
      .text-warning { color: var(--warning-color); }
      .text-info { color: var(--info-color); }

      .bg-success-light { background: var(--success-light); }
      .bg-danger-light { background: var(--danger-light); }
      .bg-warning-light { background: var(--warning-light); }
      .bg-info-light { background: var(--info-light); }

      .rounded-sm { border-radius: var(--radius-sm); }
      .rounded-md { border-radius: var(--radius-md); }
      .rounded-lg { border-radius: var(--radius-lg); }
      .rounded-xl { border-radius: var(--radius-xl); }
      .rounded-full { border-radius: var(--radius-full); }

      .shadow-sm { box-shadow: var(--shadow-sm); }
      .shadow-md { box-shadow: var(--shadow-md); }
      .shadow-lg { box-shadow: var(--shadow-lg); }
      .shadow-xl { box-shadow: var(--shadow-xl); }

      /* 现代化请求监控面板 */
      .request-monitor {
        background: rgba(255, 255, 255, 0.02);
        border-radius: var(--radius-xl);
        padding: var(--space-6);
        backdrop-filter: blur(10px);
      }
      .request-monitor .monitor-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--space-6);
        padding: var(--space-4);
        background: rgba(255, 255, 255, 0.05);
        border-radius: var(--radius-lg);
        backdrop-filter: blur(10px);
      }

      .request-monitor .monitor-stats {
        display: flex;
        gap: var(--space-4);
        font-size: 0.75rem;
        color: var(--text-secondary);
      }

      .request-monitor .stat-item {
        display: flex;
        align-items: center;
        gap: var(--space-2);
        padding: var(--space-2) var(--space-3);
        background: rgba(255, 255, 255, 0.1);
        border-radius: var(--radius-lg);
        backdrop-filter: blur(5px);
      }

      .request-monitor .stat-dot {
        width: 10px;
        height: 10px;
        border-radius: var(--radius-full);
        box-shadow: 0 0 10px currentColor;
      }

      .request-monitor .stat-dot.success {
        background: var(--success-color);
        color: var(--success-color);
      }
      .request-monitor .stat-dot.error {
        background: var(--danger-color);
        color: var(--danger-color);
      }
      .request-monitor .stat-dot.pending {
        background: var(--warning-color);
        color: var(--warning-color);
      }

      .request-monitor .monitor-controls {
        display: flex;
        gap: var(--space-2);
      }

      .request-monitor .request-filters {
        display: flex;
        gap: var(--space-3);
        margin-bottom: var(--space-6);
        padding: var(--space-4);
        background: rgba(255, 255, 255, 0.05);
        border-radius: var(--radius-lg);
        backdrop-filter: blur(10px);
      }

      .request-monitor .filter-input {
        flex: 1;
        padding: var(--space-3) var(--space-4);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: var(--radius-lg);
        font-size: 0.75rem;
        background: rgba(255, 255, 255, 0.05);
        color: var(--text-primary);
        backdrop-filter: blur(5px);
        transition: all 0.3s var(--ease-out);
      }

      .request-monitor .filter-input:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
      }

      .request-monitor .filter-select {
        padding: var(--space-3) var(--space-4);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: var(--radius-lg);
        font-size: 0.75rem;
        background: rgba(255, 255, 255, 0.05);
        color: var(--text-primary);
        backdrop-filter: blur(5px);
        transition: all 0.3s var(--ease-out);
      }

      .request-monitor .request-list {
        max-height: 400px;
        overflow-y: auto;
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: var(--radius-xl);
        background: rgba(255, 255, 255, 0.02);
        backdrop-filter: blur(10px);
      }

      .request-monitor .request-list::-webkit-scrollbar {
        width: 6px;
      }

      .request-monitor .request-list::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.05);
        border-radius: var(--radius-full);
      }

      .request-monitor .request-list::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.2);
        border-radius: var(--radius-full);
      }

      .request-monitor .request-item {
        padding: var(--space-4);
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        cursor: pointer;
        transition: all 0.3s var(--ease-out);
        position: relative;
        overflow: hidden;
      }

      .request-monitor .request-item::before {
        content: '';
        position: absolute;
        inset: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.02));
        opacity: 0;
        transition: opacity 0.3s var(--ease-out);
      }

      .request-monitor .request-item:hover {
        background: rgba(255, 255, 255, 0.05);
        transform: translateX(4px);
      }

      .request-monitor .request-item:hover::before {
        opacity: 1;
      }

      .request-monitor .request-item:last-child {
        border-bottom: none;
      }

      .request-monitor .request-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 0.25rem;
      }

      .request-monitor .request-method {
        padding: var(--space-2) var(--space-3);
        border-radius: var(--radius-lg);
        font-size: 0.625rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: var(--shadow-sm);
        transition: all 0.3s var(--ease-out);
      }

      .request-monitor .request-method:hover {
        transform: scale(1.05);
        box-shadow: var(--shadow-md);
      }

      .request-monitor .request-method.GET {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        color: white;
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
      }
      .request-monitor .request-method.POST {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
      }
      .request-monitor .request-method.PUT {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        color: white;
        box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
      }
      .request-monitor .request-method.DELETE {
        background: linear-gradient(135deg, #ef4444, #dc2626);
        color: white;
        box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
      }
      .request-monitor .request-method.PATCH {
        background: linear-gradient(135deg, #8b5cf6, #7c3aed);
        color: white;
        box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
      }

      .request-monitor .request-status {
        display: flex;
        align-items: center;
        gap: var(--space-2);
        font-size: 0.75rem;
      }

      .request-monitor .status-code {
        padding: var(--space-1) var(--space-2);
        border-radius: var(--radius-md);
        font-weight: 600;
        font-size: 0.625rem;
        backdrop-filter: blur(5px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s var(--ease-out);
      }

      .request-monitor .status-code:hover {
        transform: scale(1.05);
      }

      .request-monitor .status-code.success {
        background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(16, 185, 129, 0.1));
        color: var(--success-color);
        border-color: rgba(16, 185, 129, 0.3);
      }
      .request-monitor .status-code.error {
        background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.1));
        color: var(--danger-color);
        border-color: rgba(239, 68, 68, 0.3);
      }
      .request-monitor .status-code.pending {
        background: linear-gradient(135deg, rgba(245, 158, 11, 0.2), rgba(245, 158, 11, 0.1));
        color: var(--warning-color);
        border-color: rgba(245, 158, 11, 0.3);
      }

      .request-monitor .request-time {
        color: var(--text-tertiary);
        font-size: 0.625rem;
        background: rgba(255, 255, 255, 0.05);
        padding: var(--space-1) var(--space-2);
        border-radius: var(--radius-md);
        backdrop-filter: blur(5px);
      }

      .request-monitor .request-url {
        font-size: 0.75rem;
        color: var(--text-primary);
        word-break: break-all;
        word-wrap: break-word;
        overflow-wrap: break-word;
        margin-bottom: var(--space-2);
        overflow-x: hidden;
        max-width: 100%;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        background: rgba(255, 255, 255, 0.05);
        padding: var(--space-2);
        border-radius: var(--radius-md);
        backdrop-filter: blur(5px);
      }

      .request-monitor .request-meta {
        display: flex;
        justify-content: space-between;
        font-size: 0.625rem;
        color: var(--text-secondary);
        margin-top: var(--space-2);
      }

      .request-monitor .request-size,
      .request-monitor .request-duration {
        background: rgba(255, 255, 255, 0.05);
        padding: var(--space-1) var(--space-2);
        border-radius: var(--radius-md);
        backdrop-filter: blur(5px);
        display: flex;
        align-items: center;
        gap: var(--space-1);
      }

      .request-monitor .request-size::before {
        content: "📦";
        font-size: 0.75rem;
      }

      .request-monitor .request-duration::before {
        content: "⏱️";
        font-size: 0.75rem;
      }

      .request-monitor .empty-state {
        text-align: center;
        padding: var(--space-12);
        color: var(--text-secondary);
        background: rgba(255, 255, 255, 0.02);
        border-radius: var(--radius-xl);
        backdrop-filter: blur(10px);
      }

      .request-monitor .empty-icon {
        font-size: 4rem;
        margin-bottom: var(--space-4);
        opacity: 0.5;
      }

      .request-monitor .empty-text {
        font-size: 0.875rem;
        font-weight: 500;
      }
    `;
  }

  /**
   * 创建悬浮按钮
   */
  private createFloatingButton(): void {
    this.floatingButton = new FloatingButton(() => {
      console.log('浮动按钮被点击');
      this.showModal();
    });

    // 将悬浮按钮添加到Shadow DOM
    const buttonElement = this.floatingButton.getElement();
    this.shadow.appendChild(buttonElement);

    console.log('浮动按钮已创建并添加到Shadow DOM', {
      buttonElement,
      shadowChildren: this.shadow.children.length,
      buttonInShadow: this.shadow.contains(buttonElement)
    });
  }

  /**
   * 显示主弹窗
   */
  private showModal(): void {
    console.log('showModal called');
    if (this.modal) {
      console.log('Modal already exists, showing it');
      this.modal.show();
      return;
    }

    console.log('Creating new modal');

    this.modal = new Modal({
      width: 800,
      height: 650,
      showTitle: false,
      container: this.shadow, // 指定Shadow DOM作为容器
      onClose: () => {
        this.cleanup();
      }
    });

    // 创建标签页容器（标题栏模式）
    this.tabContainer = new TabContainer(true);

    // 获取标题栏的标签页容器
    const headerTabsContainer = this.modal.getModal().querySelector('.header-tabs') as HTMLElement;
    if (headerTabsContainer) {
      this.tabContainer.setTabsContainer(headerTabsContainer);
    }

    // 创建账号面板
    const panelState = {
      account: '',
      password: 'Authine@123456',
      token: '',
      loading: false
    };

    this.accountPanel = new AccountPanel(panelState, {
      onAccountLogin: async (account: string) => {
        console.log('账号登录:', account);
      },
      onTokenLogin: (token: string) => {
        console.log('Token登录:', token);
      },
      onClearData: () => {
        console.log('清除数据');
      },
      onShowMessage: (message: string, type: string) => {
        console.log(`${type}: ${message}`);
      }
    });

    // 创建请求监控
    this.requestMonitor = new RequestMonitor({
      onShowMessage: (message: string, type: string) => {
        console.log(`${type}: ${message}`);
      }
    });

    // 添加标签页
    this.tabContainer.addTab('account', '账号切换', this.accountPanel.getContainer());
    this.tabContainer.addTab('monitor', '请求监控', this.requestMonitor.getContainer());

    // 设置模态框内容
    this.modal.setContent(this.tabContainer.getContainer());

    // 显示模态框（会自动添加到指定的Shadow DOM容器中）
    console.log('Showing modal');
    this.modal.show();
    console.log('Modal show() called');
  }

  /**
   * 清理资源
   */
  private cleanup(): void {
    this.modal = null;
    this.tabContainer = null;
    this.accountPanel = null;
    this.requestMonitor = null;
  }
}

// 注册自定义元素
customElements.define('account-change', AccountChangeElement);
