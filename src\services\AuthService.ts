// 认证服务

import { RsaEncrypt } from '../utils/crypto';
import { AccountStorage } from '../utils/storage';

export interface LoginCredentials {
  account: string;
  password: string;
}

export interface PublicKeyData {
  index: string;
  key: string;
}

export interface AuthCodeData {
  code: string;
}

export class AuthService {
  private static readonly DEFAULT_PASSWORD = 'Authine@123456';

  /**
   * 账号登录
   * @param credentials 登录凭据
   * @returns Promise<string> Token
   */
  static async login(credentials: LoginCredentials): Promise<string> {
    try {
      // 获取公钥
      const publicKeyData = await this.getPublicKey();

      // 获取授权码
      const authCode = await this.getAuthCode(publicKeyData, credentials);

      // 获取Token
      const token = await this.getToken(authCode.code);

      // 保存Token和账号
      AccountStorage.saveToken(token);
      AccountStorage.saveAccount(credentials.account);

      return token;
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  }

  /**
   * Token登录
   * @param token Token值
   */
  static tokenLogin(token: string): void {
    AccountStorage.saveToken(token);
  }

  /**
   * 获取公钥
   */
  private static async getPublicKey(): Promise<PublicKeyData> {
    const response = await fetch('/api/public/getKey');
    if (!response.ok) {
      throw new Error('获取公钥失败');
    }
    return await response.json();
  }

  /**
   * 获取授权码
   */
  private static async getAuthCode(
    publicKeyData: PublicKeyData,
    credentials: LoginCredentials
  ): Promise<AuthCodeData> {
    const { index, key } = publicKeyData;

    // 使用兼容JSEncrypt的加密方法
    const encryptedPassword = RsaEncrypt(credentials.password, key);

    const redirectUri = `${window.location.origin}/api/oauth/authorize?client_id=api&response_type=code&scope=read&redirect_uri=${window.location.origin}/oauth`;
    const url = `${window.location.origin}/api/login?redirect_uri=${encodeURIComponent(redirectUri)}`;
    const corpId = AccountStorage.getCorpId();

    const response = await fetch('/api/login/Authentication/get_code', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: credentials.account,
        password: encryptedPassword,
        url,
        portal: true,
        index,
        corpId
      })
    });

    if (!response.ok) {
      throw new Error('获取授权码失败');
    }

    return await response.json();
  }

  /**
   * 获取Token
   */
  private static async getToken(code: string): Promise<string> {
    const url = `${window.location.origin}/api/login/Authentication/get_token?code=${code}&url=${encodeURIComponent(`${window.location.origin}/api`)}&client_secret=c31b32364ce19ca8fcd150a417ecce58&client_id=api&redirect_uri=${encodeURIComponent(`${window.location.origin}/oauth`)}`;

    const response = await fetch(url);
    if (!response.ok) {
      throw new Error('获取Token失败');
    }

    const data = await response.json();
    return data.access_token;
  }

  /**
   * 默认账号登录
   */
  static async defaultLogin(): Promise<string> {
    return this.login({
      account: '***********',
      password: this.DEFAULT_PASSWORD
    });
  }

  /**
   * 检查是否已登录
   */
  static isLoggedIn(): boolean {
    return !!AccountStorage.getToken();
  }

  /**
   * 获取当前用户信息
   */
  static getCurrentUser(): {
    account: string | null;
    token: string | null;
    corpId: string | null;
  } {
    return {
      account: AccountStorage.getLastAccount(),
      token: AccountStorage.getToken(),
      corpId: AccountStorage.getCorpId()
    };
  }

  /**
   * 登出
   */
  static logout(): void {
    AccountStorage.clearAll();
  }

  /**
   * 刷新页面或跳转
   */
  static redirectAfterLogin(): void {
    if (window.location.href.startsWith(`${window.location.origin}/login`)) {
      window.location.href = `${window.location.origin}/`;
    } else {
      window.location.reload();
    }
  }

  /**
   * 验证账号格式
   */
  static validateAccount(account: string): boolean {
    return account.trim().length > 0;
  }

  /**
   * 验证Token格式
   */
  static validateToken(token: string): boolean {
    return token.trim().length > 0;
  }
}
